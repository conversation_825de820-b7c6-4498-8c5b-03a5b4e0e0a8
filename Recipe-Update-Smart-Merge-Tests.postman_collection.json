{"info": {"name": "Recipe Update - Smart Merge Tests", "description": "Test collection for Recipe Update API with Smart Merge implementation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "authToken", "value": "your-auth-token-here", "type": "string"}, {"key": "recipeId", "value": "1", "type": "string"}], "item": [{"name": "1. Get Current Recipe State", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Recipe data exists', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "    pm.expect(jsonData.data).to.exist;", "});", "", "// Store current recipe data for comparison", "const jsonData = pm.response.json();", "pm.collectionVariables.set('currentRecipe', JSON.stringify(jsonData.data));", "", "console.log('Current Recipe State:');", "console.log('Title:', jsonData.data.recipe_title);", "console.log('Resources:', jsonData.data.resources?.length || 0);", "console.log('Steps:', jsonData.data.steps?.length || 0);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "{{recipeId}}"]}}}, {"name": "2. Smart Merge Update - 3 New + 2 Existing", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Update successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "    pm.expect(jsonData.message).to.exist;", "});", "", "console.log('Update Response:');", "console.log('Status:', pm.response.json().status);", "console.log('Message:', pm.response.json().message);"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Smart Merge Test Recipe - Updated", "type": "text"}, {"key": "recipe_description", "value": "Testing 3 new + 2 existing files scenario with Smart Merge", "type": "text"}, {"key": "recipe_preparation_time", "value": "25", "type": "text"}, {"key": "recipe_cook_time", "value": "40", "type": "text"}, {"key": "recipePlaceholder", "type": "file", "src": [], "description": "Upload a new placeholder image file"}, {"key": "recipeFiles", "type": "file", "src": [], "description": "Upload new resource file 1 (PDF/DOC/Image)"}, {"key": "recipeFiles", "type": "file", "src": [], "description": "Upload new resource file 2 (PDF/DOC/Image)"}, {"key": "recipeFiles", "type": "file", "src": [], "description": "Upload new resource file 3 (PDF/DOC/Image)"}, {"key": "stepImages", "type": "file", "src": [], "description": "Upload new step image 1"}, {"key": "stepImages", "type": "file", "src": [], "description": "Upload new step image 2"}, {"key": "resources", "value": "[\n  {\n    \"item_id\": 101,\n    \"type\": \"item\"\n  },\n  {\n    \"item_id\": 102,\n    \"type\": \"item\"\n  },\n  {\n    \"type\": \"link\",\n    \"item_link\": \"https://youtube.com/watch?v=test-recipe-demo\",\n    \"item_link_type\": \"youtube\"\n  },\n  {\n    \"type\": \"link\",\n    \"item_link\": \"https://example.com/recipe-nutrition-guide.pdf\",\n    \"item_link_type\": \"pdf\"\n  },\n  {\n    \"type\": \"link\",\n    \"item_link\": \"https://example.com/cooking-tips-blog\",\n    \"item_link_type\": \"link\"\n  }\n]", "type": "text", "description": "2 existing files (item_id 101, 102) + 3 link resources"}, {"key": "steps", "value": "[\n  {\n    \"order\": 1,\n    \"description\": \"Prepare ingredients and wash vegetables thoroughly\",\n    \"item_id\": 301\n  },\n  {\n    \"order\": 2,\n    \"description\": \"Heat oil in a large pan over medium heat\"\n  },\n  {\n    \"order\": 3,\n    \"description\": \"Add onions and cook until translucent\",\n    \"item_id\": 303\n  },\n  {\n    \"order\": 4,\n    \"description\": \"Add spices and cook for 2 minutes\"\n  },\n  {\n    \"order\": 5,\n    \"description\": \"Season with salt and pepper to taste\"\n  }\n]", "type": "text", "description": "5 steps: 2 with existing images, 2 with new images, 1 without image"}, {"key": "ingredients", "value": "[\n  {\n    \"id\": 1,\n    \"quantity\": 2,\n    \"measure\": \"cups\",\n    \"ingredient_name\": \"Rice\"\n  },\n  {\n    \"id\": 2,\n    \"quantity\": 1,\n    \"measure\": \"tbsp\",\n    \"ingredient_name\": \"Olive Oil\"\n  },\n  {\n    \"id\": 3,\n    \"quantity\": 1,\n    \"measure\": \"medium\",\n    \"ingredient_name\": \"Onion\"\n  }\n]", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "update", "{{recipeId}}"]}}}, {"name": "3. Verify Updated Recipe State", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Recipe updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "    pm.expect(jsonData.data).to.exist;", "    pm.expect(jsonData.data.recipe_title).to.equal('Smart Merge Test Recipe - Updated');", "});", "", "// Compare with original state", "const currentRecipe = JSON.parse(pm.collectionVariables.get('currentRecipe') || '{}');", "const updatedRecipe = pm.response.json().data;", "", "console.log('=== SMART MERGE TEST RESULTS ===');", "console.log('Title Updated:', updatedRecipe.recipe_title);", "console.log('Placeholder Updated:', updatedRecipe.recipe_placeholder !== currentRecipe.recipe_placeholder);", "", "// Count active resources", "const activeResources = updatedRecipe.resources?.filter(r => r.status === 'active') || [];", "const fileResources = activeResources.filter(r => r.type === 'item');", "const linkResources = activeResources.filter(r => r.type === 'link');", "", "console.log('Active Resources:', activeResources.length);", "console.log('- File Resources:', fileResources.length);", "console.log('- Link Resources:', linkResources.length);", "", "// Verify expected counts", "pm.test('Expected resource counts', function () {", "    pm.expect(activeResources.length).to.be.at.least(5); // 2 existing + 3 new + 3 links = 8 minimum", "    pm.expect(linkResources.length).to.equal(3); // 3 link resources added", "});", "", "// Check steps", "console.log('Total Steps:', updatedRecipe.steps?.length || 0);", "const stepsWithImages = updatedRecipe.steps?.filter(s => s.item_id) || [];", "console.log('Steps with Images:', stepsWithImages.length);", "", "pm.test('Expected step counts', function () {", "    pm.expect(updatedRecipe.steps?.length).to.equal(5); // 5 steps total", "    pm.expect(stepsWithImages.length).to.be.at.least(2); // At least 2 steps should have images", "});", "", "console.log('=== SMART MERGE VALIDATION COMPLETE ===');"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "{{recipeId}}"]}}}, {"name": "4. Test Keep All Existing (No Media Changes)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Basic info update successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "});", "", "console.log('Basic info update completed - all media should be preserved');"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Basic Info Update Only - Media Preserved", "type": "text"}, {"key": "recipe_description", "value": "Testing that existing media is preserved when no media fields are provided", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "update", "{{recipeId}}"]}}}, {"name": "5. Test Remove All Resources", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Resources removal successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "});", "", "console.log('All resources should be deactivated');"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Remove All Resources Test", "type": "text"}, {"key": "resources", "value": "[]", "type": "text", "description": "Empty array to remove all resources"}]}, "url": {"raw": "{{baseUrl}}/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "update", "{{recipeId}}"]}}}]}