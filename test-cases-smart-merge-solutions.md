# Smart Merge Implementation Solutions

## Solution 1: Enhanced updateRecipeResources Function

```typescript
const updateRecipeResources = async (
  recipeId: number,
  resources: any[],
  userId: number,
  organizationId: string,
  ipAddress: string,
  userAgent: string,
  transaction: any,
  createHistory: boolean = true,
  smartMerge: boolean = true
) => {
  try {
    if (smartMerge) {
      // SMART MERGE LOGIC
      if (!resources || resources.length === 0) {
        // No resources provided - keep all existing active
        console.log('No resources provided, preserving existing resources');
        return;
      }

      // Get current active resources
      const currentResources = await RecipeResources.findAll({
        where: { 
          recipe_id: recipeId, 
          status: RecipeResourceStatus.active 
        },
        transaction
      });

      // Extract item_ids from provided resources
      const providedItemIds = resources
        .filter(r => r.item_id)
        .map(r => r.item_id);

      // Deactivate resources that are NOT in the provided list
      if (currentResources.length > 0) {
        const currentItemIds = currentResources.map(r => r.item_id);
        const itemsToDeactivate = currentItemIds.filter(id => 
          id && !providedItemIds.includes(id)
        );

        if (itemsToDeactivate.length > 0) {
          await RecipeResources.update(
            { status: RecipeResourceStatus.inactive, updated_by: userId },
            { 
              where: { 
                recipe_id: recipeId,
                item_id: { [Op.in]: itemsToDeactivate }
              }, 
              transaction 
            }
          );
          console.log(`Deactivated ${itemsToDeactivate.length} resources`);
        }
      }
    } else {
      // ORIGINAL LOGIC - Deactivate all existing
      await RecipeResources.update(
        { status: RecipeResourceStatus.inactive, updated_by: userId },
        { where: { recipe_id: recipeId }, transaction }
      );
    }

    // Create/update all provided resources
    if (resources && resources.length > 0) {
      // Validate item_ids exist
      const validResources = [];
      for (const resource of resources) {
        if (resource.item_id) {
          const itemExists = await Item.findByPk(resource.item_id, { transaction });
          if (itemExists) {
            validResources.push(resource);
          } else {
            console.warn(`Item ID ${resource.item_id} not found, skipping resource`);
          }
        } else if (resource.item_link) {
          validResources.push(resource);
        }
      }

      if (validResources.length > 0) {
        const resourceData = validResources.map((resource: any) => ({
          recipe_id: recipeId,
          type: resource.type || 'item',
          item_id: resource.item_id,
          item_link: resource.item_link,
          item_link_type: resource.item_link_type,
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        await RecipeResources.bulkCreate(resourceData, {
          transaction,
          updateOnDuplicate: [
            "type", "item_id", "item_link", "item_link_type", 
            "status", "updated_by", "updated_at"
          ],
        });
        console.log(`Created/updated ${validResources.length} resources`);
      }
    }

    // Create history if requested
    if (createHistory) {
      await createRecipeHistory({
        recipe_id: recipeId,
        action: RecipeHistoryAction.updated,
        field_name: "resources",
        old_value: null,
        new_value: JSON.stringify(resources),
        description: `Recipe resources updated (${resources?.length || 0} resources)`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      }, transaction);
    }

  } catch (error) {
    console.error('Error in updateRecipeResources:', error);
    throw error;
  }
};
```

## Solution 2: Enhanced updateRecipeSteps Function

```typescript
const updateRecipeSteps = async (
  recipeId: number,
  steps: any[],
  userId: number,
  organizationId: string,
  ipAddress: string,
  userAgent: string,
  transaction: any,
  createHistory: boolean = true,
  smartMerge: boolean = true
) => {
  try {
    if (smartMerge && (!steps || steps.length === 0)) {
      // No steps provided - keep existing steps
      console.log('No steps provided, preserving existing steps');
      return;
    }

    // Always delete existing steps (current behavior maintained)
    // This is because steps have order dependencies
    await RecipeSteps.destroy({
      where: { recipe_id: recipeId },
      transaction,
    });

    // Create new steps
    if (steps && steps.length > 0) {
      // Validate step item_ids if provided
      const validSteps = [];
      for (const step of steps) {
        const validStep = { ...step };
        
        if (step.item_id) {
          const itemExists = await Item.findByPk(step.item_id, { transaction });
          if (!itemExists) {
            console.warn(`Step item ID ${step.item_id} not found, setting to null`);
            validStep.item_id = null;
          }
        }
        
        validSteps.push(validStep);
      }

      const stepData = validSteps.map((step: any) => ({
        recipe_id: recipeId,
        item_id: step.item_id || null,
        recipe_step_order: step.order,
        recipe_step_description: step.description,
        status: RecipeStepsStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await RecipeSteps.bulkCreate(stepData, { transaction });
      console.log(`Created ${validSteps.length} steps`);
    }

    // Create history if requested
    if (createHistory) {
      await createRecipeHistory({
        recipe_id: recipeId,
        action: RecipeHistoryAction.updated,
        field_name: "steps",
        old_value: null,
        new_value: JSON.stringify(steps),
        description: `Recipe steps updated (${steps?.length || 0} steps)`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      }, transaction);
    }

  } catch (error) {
    console.error('Error in updateRecipeSteps:', error);
    throw error;
  }
};
```

## Solution 3: Smart Placeholder Handling

```typescript
// In updateRecipe function - Enhanced placeholder logic
let updatedPlaceholder = recipe_placeholder; // Default to form value

if (req.files && files.recipePlaceholder && files.recipePlaceholder.length > 0) {
  // New placeholder uploaded - process it
  const placeholderFile = files.recipePlaceholder[0];
  // ... existing upload logic
  updatedPlaceholder = placeholderFile.item_id;
} else if (recipe_placeholder === null) {
  // Explicitly set to null - remove placeholder
  updatedPlaceholder = null;
} else if (recipe_placeholder === undefined) {
  // Not provided - keep existing
  updatedPlaceholder = existingRecipe.recipe_placeholder;
} else {
  // Specific value provided - use it
  updatedPlaceholder = recipe_placeholder;
}

// Update recipe with smart placeholder logic
if (updatedPlaceholder !== existingRecipe.recipe_placeholder) {
  updateData.recipe_placeholder = updatedPlaceholder;
}
```

## Solution 4: Test Data Setup Helper

```typescript
// Test helper function to create test data
export const createTestRecipeWithMedia = async (transaction: any) => {
  // Create test recipe
  const recipe = await Recipe.create({
    recipe_title: "Test Recipe",
    recipe_description: "Test Description",
    has_recipe_public_visibility: true,
    has_recipe_private_visibility: true,
    recipe_status: RecipeStatus.draft,
    organization_id: "test-org",
    recipe_placeholder: 123, // existing placeholder
    created_by: 1,
    updated_by: 1,
  }, { transaction });

  // Create test resources
  await RecipeResources.bulkCreate([
    {
      recipe_id: recipe.id,
      type: RecipeResourceType.item,
      item_id: 101,
      status: RecipeResourceStatus.active,
      organization_id: "test-org",
      created_by: 1,
      updated_by: 1,
    },
    {
      recipe_id: recipe.id,
      type: RecipeResourceType.item,
      item_id: 102,
      status: RecipeResourceStatus.active,
      organization_id: "test-org",
      created_by: 1,
      updated_by: 1,
    }
  ], { transaction });

  // Create test steps
  await RecipeSteps.bulkCreate([
    {
      recipe_id: recipe.id,
      item_id: 301,
      recipe_step_order: 1,
      recipe_step_description: "Step 1",
      status: RecipeStepsStatus.active,
      organization_id: "test-org",
      created_by: 1,
      updated_by: 1,
    },
    {
      recipe_id: recipe.id,
      item_id: 302,
      recipe_step_order: 2,
      recipe_step_description: "Step 2",
      status: RecipeStepsStatus.active,
      organization_id: "test-org",
      created_by: 1,
      updated_by: 1,
    }
  ], { transaction });

  return recipe;
};
```

## Solution 5: Test Execution Framework

```typescript
// Test runner for Smart Merge scenarios
export const runSmartMergeTests = async () => {
  const testResults = [];

  // Test Case 2.2: Add New Resources Only
  const test_2_2 = async () => {
    const transaction = await sequelize.transaction();
    try {
      const recipe = await createTestRecipeWithMedia(transaction);

      // Simulate new file uploads
      const newResources = [
        { item_id: 101, type: "item" }, // existing
        { item_id: 102, type: "item" }, // existing
        { item_id: 201, type: "item" }, // new (simulated)
        { item_id: 202, type: "item" }  // new (simulated)
      ];

      await updateRecipeResources(
        recipe.id,
        newResources,
        1, // userId
        "test-org",
        "127.0.0.1",
        "test-agent",
        transaction,
        false, // no history
        true   // smart merge
      );

      // Verify results
      const finalResources = await RecipeResources.findAll({
        where: { recipe_id: recipe.id },
        transaction
      });

      const activeResources = finalResources.filter(r => r.status === 'active');
      const inactiveResources = finalResources.filter(r => r.status === 'inactive');

      await transaction.rollback();

      return {
        testName: "Test 2.2: Add New Resources Only",
        passed: activeResources.length === 4 && inactiveResources.length === 0,
        expected: "4 active resources, 0 inactive",
        actual: `${activeResources.length} active, ${inactiveResources.length} inactive`,
        details: {
          activeItemIds: activeResources.map(r => r.item_id),
          inactiveItemIds: inactiveResources.map(r => r.item_id)
        }
      };
    } catch (error) {
      await transaction.rollback();
      return {
        testName: "Test 2.2: Add New Resources Only",
        passed: false,
        error: error.message
      };
    }
  };

  testResults.push(await test_2_2());
  return testResults;
};
```
