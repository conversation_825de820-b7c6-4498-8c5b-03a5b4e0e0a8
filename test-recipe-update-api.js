// Test Recipe Update API - Smart Merge Implementation
// Run this test to validate the 3 new + 2 existing files scenario

const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:3000/api/v1'; // Adjust your API URL
const AUTH_TOKEN = 'your-auth-token-here'; // Replace with actual token
const RECIPE_ID = 1; // Replace with actual recipe ID to test

// Test data
const testConfig = {
  recipeId: RECIPE_ID,
  authToken: AUTH_TOKEN,
  baseUrl: API_BASE_URL
};

// Helper function to create test files
const createTestFile = (filename, content = 'Test file content') => {
  const filePath = path.join(__dirname, 'test-files', filename);
  
  // Create test-files directory if it doesn't exist
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  // Create test file
  fs.writeFileSync(filePath, content);
  return filePath;
};

// Test Case 1: Get current recipe state (before update)
const getCurrentRecipe = async () => {
  try {
    console.log('🔍 Getting current recipe state...');
    
    const response = await axios.get(`${testConfig.baseUrl}/recipes/${testConfig.recipeId}`, {
      headers: {
        'Authorization': `Bearer ${testConfig.authToken}`
      }
    });
    
    const recipe = response.data.data;
    console.log('✅ Current recipe retrieved');
    console.log('📊 Current state:');
    console.log(`   - Title: ${recipe.recipe_title}`);
    console.log(`   - Placeholder: ${recipe.recipe_placeholder || 'None'}`);
    console.log(`   - Resources: ${recipe.resources?.length || 0}`);
    console.log(`   - Steps: ${recipe.steps?.length || 0}`);
    
    if (recipe.resources?.length > 0) {
      console.log('📁 Current resources:');
      recipe.resources.forEach((resource, index) => {
        console.log(`   ${index + 1}. ID: ${resource.item_id}, Type: ${resource.type}, Status: ${resource.status}`);
      });
    }
    
    if (recipe.steps?.length > 0) {
      console.log('📝 Current steps:');
      recipe.steps.forEach((step, index) => {
        console.log(`   ${step.recipe_step_order}. ${step.recipe_step_description} (Image ID: ${step.item_id || 'None'})`);
      });
    }
    
    return recipe;
    
  } catch (error) {
    console.error('❌ Failed to get current recipe:', error.response?.data || error.message);
    throw error;
  }
};

// Test Case 2: Complete Smart Merge Update
const testCompleteUpdate = async (currentRecipe) => {
  try {
    console.log('\n🚀 Testing complete Smart Merge update...');
    
    // Create test files
    const testFiles = {
      placeholder: createTestFile('new-placeholder.jpg', 'New placeholder image content'),
      resource1: createTestFile('new-resource-1.pdf', 'New resource file 1 content'),
      resource2: createTestFile('new-resource-2.doc', 'New resource file 2 content'),
      resource3: createTestFile('new-resource-3.jpg', 'New resource file 3 content'),
      stepImage1: createTestFile('new-step-1.jpg', 'New step image 1 content'),
      stepImage2: createTestFile('new-step-2.jpg', 'New step image 2 content')
    };
    
    // Build FormData
    const formData = new FormData();
    
    // Basic recipe info
    formData.append('recipe_title', 'Smart Merge Test Recipe - Updated');
    formData.append('recipe_description', 'Testing 3 new + 2 existing files scenario');
    formData.append('recipe_preparation_time', '25');
    formData.append('recipe_cook_time', '40');
    
    // New placeholder image
    formData.append('recipePlaceholder', fs.createReadStream(testFiles.placeholder));
    
    // 3 new resource files
    formData.append('recipeFiles', fs.createReadStream(testFiles.resource1));
    formData.append('recipeFiles', fs.createReadStream(testFiles.resource2));
    formData.append('recipeFiles', fs.createReadStream(testFiles.resource3));
    
    // 2 new step images
    formData.append('stepImages', fs.createReadStream(testFiles.stepImage1));
    formData.append('stepImages', fs.createReadStream(testFiles.stepImage2));
    
    // Resources array (2 existing + links)
    const existingResourcesToKeep = currentRecipe.resources?.slice(0, 2) || []; // Keep first 2 existing
    const resourcesArray = [
      // 2 existing resources to keep
      ...existingResourcesToKeep.map(resource => ({
        item_id: resource.item_id,
        type: "item"
      })),
      // Link resources
      {
        type: "link",
        item_link: "https://youtube.com/watch?v=test-recipe-demo",
        item_link_type: "youtube"
      },
      {
        type: "link",
        item_link: "https://example.com/recipe-nutrition-guide.pdf",
        item_link_type: "pdf"
      },
      {
        type: "link",
        item_link: "https://example.com/cooking-tips-blog",
        item_link_type: "link"
      }
    ];
    
    formData.append('resources', JSON.stringify(resourcesArray));
    
    // Steps array (mix of existing and new images)
    const existingStepsToKeep = currentRecipe.steps?.slice(0, 2) || []; // Keep first 2 existing steps
    const stepsArray = [
      // Keep existing step 1 with its image
      {
        order: 1,
        description: existingStepsToKeep[0]?.recipe_step_description || "Prepare ingredients thoroughly",
        item_id: existingStepsToKeep[0]?.item_id || null
      },
      // New step with new image (from stepImages[0])
      {
        order: 2,
        description: "Heat oil in a large pan over medium heat"
        // Will get new image from stepImages upload
      },
      // Keep existing step 2 with its image
      {
        order: 3,
        description: existingStepsToKeep[1]?.recipe_step_description || "Add onions and cook until translucent",
        item_id: existingStepsToKeep[1]?.item_id || null
      },
      // New step with new image (from stepImages[1])
      {
        order: 4,
        description: "Add spices and cook for 2 minutes"
        // Will get new image from stepImages upload
      },
      // New step without image
      {
        order: 5,
        description: "Season with salt and pepper to taste"
        // No image
      }
    ];
    
    formData.append('steps', JSON.stringify(stepsArray));
    
    // Additional data
    const ingredientsArray = [
      { id: 1, quantity: 2, measure: "cups", ingredient_name: "Rice" },
      { id: 2, quantity: 1, measure: "tbsp", ingredient_name: "Olive Oil" },
      { id: 3, quantity: 1, measure: "medium", ingredient_name: "Onion" }
    ];
    formData.append('ingredients', JSON.stringify(ingredientsArray));
    
    // Send update request
    console.log('📤 Sending update request...');
    console.log('📋 Update payload summary:');
    console.log(`   - New placeholder: Yes`);
    console.log(`   - New resource files: 3`);
    console.log(`   - Existing resources to keep: ${existingResourcesToKeep.length}`);
    console.log(`   - Link resources: 3`);
    console.log(`   - New step images: 2`);
    console.log(`   - Total steps: 5`);
    
    const response = await axios.put(
      `${testConfig.baseUrl}/recipes/update/${testConfig.recipeId}`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${testConfig.authToken}`,
          ...formData.getHeaders()
        },
        timeout: 30000 // 30 second timeout for file uploads
      }
    );
    
    console.log('✅ Update successful!');
    console.log('📊 Response status:', response.status);
    console.log('📄 Response message:', response.data.message);
    
    // Clean up test files
    Object.values(testFiles).forEach(filePath => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    });
    
    return response.data;
    
  } catch (error) {
    console.error('❌ Update failed:', error.response?.data || error.message);
    
    // Clean up test files on error
    const testFilesDir = path.join(__dirname, 'test-files');
    if (fs.existsSync(testFilesDir)) {
      fs.rmSync(testFilesDir, { recursive: true, force: true });
    }
    
    throw error;
  }
};

// Test Case 3: Verify updated recipe state
const verifyUpdatedRecipe = async () => {
  try {
    console.log('\n🔍 Verifying updated recipe state...');
    
    const response = await axios.get(`${testConfig.baseUrl}/recipes/${testConfig.recipeId}`, {
      headers: {
        'Authorization': `Bearer ${testConfig.authToken}`
      }
    });
    
    const updatedRecipe = response.data.data;
    console.log('✅ Updated recipe retrieved');
    console.log('📊 Updated state:');
    console.log(`   - Title: ${updatedRecipe.recipe_title}`);
    console.log(`   - Placeholder: ${updatedRecipe.recipe_placeholder || 'None'}`);
    console.log(`   - Resources: ${updatedRecipe.resources?.length || 0}`);
    console.log(`   - Steps: ${updatedRecipe.steps?.length || 0}`);
    
    if (updatedRecipe.resources?.length > 0) {
      console.log('📁 Updated resources:');
      const activeResources = updatedRecipe.resources.filter(r => r.status === 'active');
      const inactiveResources = updatedRecipe.resources.filter(r => r.status === 'inactive');
      
      console.log(`   Active resources (${activeResources.length}):`);
      activeResources.forEach((resource, index) => {
        const type = resource.type === 'item' ? 'File' : 'Link';
        const identifier = resource.item_id || resource.item_link;
        console.log(`   ${index + 1}. ${type}: ${identifier} (${resource.item_link_type || 'file'})`);
      });
      
      if (inactiveResources.length > 0) {
        console.log(`   Inactive resources (${inactiveResources.length}):`);
        inactiveResources.forEach((resource, index) => {
          console.log(`   ${index + 1}. ID: ${resource.item_id}, Status: ${resource.status}`);
        });
      }
    }
    
    if (updatedRecipe.steps?.length > 0) {
      console.log('📝 Updated steps:');
      updatedRecipe.steps.forEach((step) => {
        const imageStatus = step.item_id ? `Image ID: ${step.item_id}` : 'No image';
        console.log(`   ${step.recipe_step_order}. ${step.recipe_step_description} (${imageStatus})`);
      });
    }
    
    return updatedRecipe;
    
  } catch (error) {
    console.error('❌ Failed to verify updated recipe:', error.response?.data || error.message);
    throw error;
  }
};

// Main test runner
const runSmartMergeTest = async () => {
  console.log('🧪 Starting Smart Merge API Test');
  console.log('=' .repeat(50));
  
  try {
    // Step 1: Get current state
    const currentRecipe = await getCurrentRecipe();
    
    // Step 2: Perform complete update
    const updateResult = await testCompleteUpdate(currentRecipe);
    
    // Step 3: Verify results
    const updatedRecipe = await verifyUpdatedRecipe();
    
    // Step 4: Summary
    console.log('\n🎉 Test Summary:');
    console.log('=' .repeat(30));
    console.log('✅ All tests passed successfully!');
    console.log(`📈 Resources: ${currentRecipe.resources?.length || 0} → ${updatedRecipe.resources?.filter(r => r.status === 'active').length || 0} active`);
    console.log(`📈 Steps: ${currentRecipe.steps?.length || 0} → ${updatedRecipe.steps?.length || 0}`);
    console.log('🔄 Smart Merge working correctly!');
    
  } catch (error) {
    console.log('\n❌ Test Failed:');
    console.log('=' .repeat(20));
    console.error(error.message);
    process.exit(1);
  }
};

// Run the test
if (require.main === module) {
  runSmartMergeTest();
}

module.exports = {
  runSmartMergeTest,
  getCurrentRecipe,
  testCompleteUpdate,
  verifyUpdatedRecipe
};
