-- Test SQL queries to verify the table structure exists
-- Run these queries in your MySQL database to verify the tables exist

-- Check if the main tables exist
SHOW TABLES LIKE 'mo_ingredients_category';
SHOW TABLES LIKE 'mo_ingredients_attributes';
SHOW TABLES LIKE 'mo_food_attributes';
SHOW TABLES LIKE 'mo_category';
SHOW TABLES LIKE 'mo_ingredients';
SHOW TABLES LIKE 'mo_recipe_ingredients';

-- Check the structure of the junction tables
DESCRIBE mo_ingredients_category;
DESCRIBE mo_ingredients_attributes;

-- Test the ingredient categories query
SELECT 'ing_cat' as t, ic.ingredient_id as parent_id, c.id, c.category_name as n, 
       c.category_slug as s, c.category_status as st,
       ci.id as i_id, ci.item_mime_type as i_type,
       CASE WHEN ci.item_location IS NOT NULL
         THEN CONCAT('http://localhost:3000/backend-api/v1/public/user/get-file?location=', ci.item_location)
         ELSE NULL END as i_url
FROM mo_recipe_ingredients ri
JOIN mo_ingredients_category ic ON ri.ingredient_id = ic.ingredient_id
JOIN mo_category c ON ic.category_id = c.id
LEFT JOIN nv_items ci ON c.category_icon = ci.id
WHERE ri.recipe_id = 1 AND ri.recipe_ingredient_status = 'active'
  AND ic.ingredient_category_status = 'active' AND c.category_status = 'active'
ORDER BY ic.ingredient_id, c.category_name
LIMIT 5;

-- Test the ingredient attributes query
SELECT 'ing_nutr' as t, ia.ingredient_id as parent_id, fa.id, fa.attribute_title as n,
       fa.attribute_slug as s, fa.attribute_type as st,
       ai.id as i_id, ai.item_mime_type as i_type,
       CASE WHEN ai.item_location IS NOT NULL
         THEN CONCAT('http://localhost:3000/backend-api/v1/public/user/get-file?location=', ai.item_location)
         ELSE NULL END as i_url,
       ia.unit as a1, ia.unit_of_measure as a2
FROM mo_recipe_ingredients ri
JOIN mo_ingredients_attributes ia ON ri.ingredient_id = ia.ingredient_id
JOIN mo_food_attributes fa ON ia.attributes_id = fa.id
LEFT JOIN nv_items ai ON fa.attribute_icon = ai.id
WHERE ri.recipe_id = 1 AND ri.recipe_ingredient_status = 'active'
  AND ia.ingredient_attributes_status = 'active' AND fa.attribute_status = 'active'
  AND fa.attribute_type = 'nutrition'
ORDER BY ia.ingredient_id, fa.attribute_title
LIMIT 5;

-- Check if there's any data in the junction tables
SELECT COUNT(*) as ingredient_categories_count FROM mo_ingredients_category WHERE ingredient_category_status = 'active';
SELECT COUNT(*) as ingredient_attributes_count FROM mo_ingredients_attributes WHERE ingredient_attributes_status = 'active';

-- Check attribute types available
SELECT DISTINCT attribute_type, COUNT(*) as count 
FROM mo_food_attributes 
WHERE attribute_status = 'active' 
GROUP BY attribute_type;
