#!/bin/bash

# Test Recipe Update API - Smart Merge Implementation
# Simple curl-based test for the 3 new + 2 existing files scenario

# Configuration - UPDATE THESE VALUES
API_BASE_URL="http://localhost:3000/api/v1"
AUTH_TOKEN="your-auth-token-here"
RECIPE_ID="1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Recipe Update API - Smart Merge${NC}"
echo "=================================================="

# Step 1: Get current recipe state
echo -e "\n${YELLOW}🔍 Step 1: Getting current recipe state...${NC}"

CURRENT_RECIPE=$(curl -s -X GET \
  "${API_BASE_URL}/recipes/${RECIPE_ID}" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -H "Content-Type: application/json")

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Current recipe retrieved${NC}"
    echo "Current recipe data:"
    echo "$CURRENT_RECIPE" | jq '.data | {title: .recipe_title, placeholder: .recipe_placeholder, resources: (.resources | length), steps: (.steps | length)}'
    
    # Extract existing resource IDs for testing
    EXISTING_RESOURCES=$(echo "$CURRENT_RECIPE" | jq -r '.data.resources[0:2] | map({item_id: .item_id, type: "item"}) | @json' 2>/dev/null || echo '[]')
    echo "Existing resources to keep: $EXISTING_RESOURCES"
else
    echo -e "${RED}❌ Failed to get current recipe${NC}"
    exit 1
fi

# Step 2: Create test files
echo -e "\n${YELLOW}🔧 Step 2: Creating test files...${NC}"

mkdir -p test-files

# Create test files with actual content
echo "Test placeholder image content" > test-files/new-placeholder.jpg
echo "Test resource file 1 - PDF content" > test-files/new-resource-1.pdf
echo "Test resource file 2 - DOC content" > test-files/new-resource-2.doc
echo "Test resource file 3 - Image content" > test-files/new-resource-3.jpg
echo "Test step image 1 content" > test-files/new-step-1.jpg
echo "Test step image 2 content" > test-files/new-step-2.jpg

echo -e "${GREEN}✅ Test files created${NC}"

# Step 3: Prepare resources array (2 existing + 3 links)
echo -e "\n${YELLOW}🔧 Step 3: Preparing resources array...${NC}"

RESOURCES_ARRAY=$(cat <<EOF
[
  $(echo "$EXISTING_RESOURCES" | jq -r '. | @json' | sed 's/^\[//;s/\]$//' | sed 's/$/,/' | head -c -2),
  {
    "type": "link",
    "item_link": "https://youtube.com/watch?v=test-recipe-demo",
    "item_link_type": "youtube"
  },
  {
    "type": "link", 
    "item_link": "https://example.com/recipe-nutrition-guide.pdf",
    "item_link_type": "pdf"
  },
  {
    "type": "link",
    "item_link": "https://example.com/cooking-tips-blog",
    "item_link_type": "link"
  }
]
EOF
)

echo "Resources array prepared:"
echo "$RESOURCES_ARRAY" | jq '.'

# Step 4: Prepare steps array
echo -e "\n${YELLOW}🔧 Step 4: Preparing steps array...${NC}"

STEPS_ARRAY='[
  {
    "order": 1,
    "description": "Prepare ingredients and wash vegetables thoroughly",
    "item_id": 301
  },
  {
    "order": 2,
    "description": "Heat oil in a large pan over medium heat"
  },
  {
    "order": 3,
    "description": "Add onions and cook until translucent",
    "item_id": 303
  },
  {
    "order": 4,
    "description": "Add spices and cook for 2 minutes"
  },
  {
    "order": 5,
    "description": "Season with salt and pepper to taste"
  }
]'

echo "Steps array prepared:"
echo "$STEPS_ARRAY" | jq '.'

# Step 5: Perform the update
echo -e "\n${YELLOW}🚀 Step 5: Performing Smart Merge update...${NC}"

echo "Update payload summary:"
echo "- New placeholder: Yes"
echo "- New resource files: 3"
echo "- Existing resources to keep: 2"
echo "- Link resources: 3"
echo "- New step images: 2"
echo "- Total steps: 5"

UPDATE_RESPONSE=$(curl -s -X PUT \
  "${API_BASE_URL}/recipes/update/${RECIPE_ID}" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -F "recipe_title=Smart Merge Test Recipe - Updated" \
  -F "recipe_description=Testing 3 new + 2 existing files scenario" \
  -F "recipe_preparation_time=25" \
  -F "recipe_cook_time=40" \
  -F "recipePlaceholder=@test-files/new-placeholder.jpg" \
  -F "recipeFiles=@test-files/new-resource-1.pdf" \
  -F "recipeFiles=@test-files/new-resource-2.doc" \
  -F "recipeFiles=@test-files/new-resource-3.jpg" \
  -F "stepImages=@test-files/new-step-1.jpg" \
  -F "stepImages=@test-files/new-step-2.jpg" \
  -F "resources=${RESOURCES_ARRAY}" \
  -F "steps=${STEPS_ARRAY}")

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Update request completed${NC}"
    echo "Update response:"
    echo "$UPDATE_RESPONSE" | jq '.'
    
    # Check if update was successful
    UPDATE_STATUS=$(echo "$UPDATE_RESPONSE" | jq -r '.status // false')
    if [ "$UPDATE_STATUS" = "true" ]; then
        echo -e "${GREEN}🎉 Update successful!${NC}"
    else
        echo -e "${RED}❌ Update failed${NC}"
        echo "Error details:"
        echo "$UPDATE_RESPONSE" | jq '.message // .error // .'
        exit 1
    fi
else
    echo -e "${RED}❌ Update request failed${NC}"
    exit 1
fi

# Step 6: Verify updated recipe
echo -e "\n${YELLOW}🔍 Step 6: Verifying updated recipe state...${NC}"

sleep 2 # Wait a moment for the update to be processed

UPDATED_RECIPE=$(curl -s -X GET \
  "${API_BASE_URL}/recipes/${RECIPE_ID}" \
  -H "Authorization: Bearer ${AUTH_TOKEN}" \
  -H "Content-Type: application/json")

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Updated recipe retrieved${NC}"
    
    # Extract key metrics
    UPDATED_TITLE=$(echo "$UPDATED_RECIPE" | jq -r '.data.recipe_title')
    UPDATED_PLACEHOLDER=$(echo "$UPDATED_RECIPE" | jq -r '.data.recipe_placeholder // "None"')
    ACTIVE_RESOURCES=$(echo "$UPDATED_RECIPE" | jq '[.data.resources[] | select(.status == "active")] | length')
    INACTIVE_RESOURCES=$(echo "$UPDATED_RECIPE" | jq '[.data.resources[] | select(.status == "inactive")] | length')
    TOTAL_STEPS=$(echo "$UPDATED_RECIPE" | jq '.data.steps | length')
    
    echo -e "\n${BLUE}📊 Updated Recipe Summary:${NC}"
    echo "Title: $UPDATED_TITLE"
    echo "Placeholder ID: $UPDATED_PLACEHOLDER"
    echo "Active Resources: $ACTIVE_RESOURCES"
    echo "Inactive Resources: $INACTIVE_RESOURCES"
    echo "Total Steps: $TOTAL_STEPS"
    
    # Show resource breakdown
    echo -e "\n${BLUE}📁 Resource Breakdown:${NC}"
    echo "$UPDATED_RECIPE" | jq -r '.data.resources[] | select(.status == "active") | 
        if .type == "item" then 
            "File: ID \(.item_id) (\(.item_link_type // "file"))"
        else 
            "Link: \(.item_link) (\(.item_link_type))"
        end'
    
    # Show steps with images
    echo -e "\n${BLUE}📝 Steps Summary:${NC}"
    echo "$UPDATED_RECIPE" | jq -r '.data.steps[] | 
        "Step \(.recipe_step_order): \(.recipe_step_description) " + 
        (if .item_id then "(Image ID: \(.item_id))" else "(No image)" end)'
    
else
    echo -e "${RED}❌ Failed to verify updated recipe${NC}"
    exit 1
fi

# Step 7: Cleanup
echo -e "\n${YELLOW}🧹 Step 7: Cleaning up test files...${NC}"
rm -rf test-files
echo -e "${GREEN}✅ Test files cleaned up${NC}"

# Final summary
echo -e "\n${GREEN}🎉 Smart Merge Test Completed Successfully!${NC}"
echo "=================================================="
echo -e "${BLUE}Test Results:${NC}"
echo "✅ Recipe update API working"
echo "✅ Smart Merge logic functioning"
echo "✅ File uploads processed"
echo "✅ Existing resources preserved"
echo "✅ Link resources added"
echo "✅ Step images handled correctly"

echo -e "\n${GREEN}🔄 Smart Merge Implementation Validated!${NC}"
