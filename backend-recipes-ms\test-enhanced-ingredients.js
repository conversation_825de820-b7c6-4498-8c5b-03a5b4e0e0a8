/**
 * Test script to verify the enhanced ingredients functionality in getRecipeByIdRaw
 * This script tests the new ingredient-related data structure
 */

// Note: Since this is a TypeScript file, we'll need to use a different approach
// This test file demonstrates the expected structure and can be used as a reference

async function testEnhancedIngredients() {
  try {
    console.log('🧪 Testing Enhanced Ingredients Functionality...\n');

    // Test with a sample recipe ID (you can change this to an existing recipe ID)
    const testRecipeId = 1;
    const testOrganizationId = 'test-org-id';

    console.log(`📋 Fetching recipe ${testRecipeId} with enhanced ingredient data...`);

    const result = await getRecipeByIdRaw(testRecipeId, testOrganizationId);

    if (!result) {
      console.log('❌ No recipe found with the given ID');
      return;
    }

    console.log('✅ Recipe fetched successfully!\n');

    // Check basic recipe info
    console.log('📖 Recipe Basic Info:');
    console.log(`   Title: ${result.recipe_title}`);
    console.log(`   ID: ${result.id}`);
    console.log(`   Status: ${result.recipe_status}\n`);

    // Check ingredients structure
    if (result.ingredients && result.ingredients.length > 0) {
      console.log(`🥕 Found ${result.ingredients.length} ingredients:\n`);

      result.ingredients.forEach((ingredient, index) => {
        console.log(`   ${index + 1}. ${ingredient.ingredient_name}`);
        console.log(`      ID: ${ingredient.id}`);
        console.log(`      Quantity: ${ingredient.ingredient_quantity} ${ingredient.measure_title || ''}`);
        console.log(`      Cost: $${ingredient.ingredient_cost || 0}`);
        console.log(`      Cost per unit: $${ingredient.cost_per_unit || 0}`);
        console.log(`      Waste percentage: ${ingredient.waste_percentage || 0}%`);

        // Check enhanced data
        if (ingredient.categories && ingredient.categories.length > 0) {
          console.log(`      📂 Categories (${ingredient.categories.length}):`);
          ingredient.categories.forEach(cat => {
            console.log(`         - ${cat.category_name} (ID: ${cat.id})`);
          });
        } else {
          console.log(`      📂 Categories: None`);
        }

        if (ingredient.nutrition_attributes && ingredient.nutrition_attributes.length > 0) {
          console.log(`      🍎 Nutrition (${ingredient.nutrition_attributes.length}):`);
          ingredient.nutrition_attributes.forEach(nutr => {
            console.log(`         - ${nutr.attribute_title}: ${nutr.unit || 'N/A'} ${nutr.unit_of_measure || ''}`);
          });
        } else {
          console.log(`      🍎 Nutrition: None`);
        }

        if (ingredient.allergen_attributes && ingredient.allergen_attributes.length > 0) {
          console.log(`      ⚠️  Allergens (${ingredient.allergen_attributes.length}):`);
          ingredient.allergen_attributes.forEach(allergen => {
            console.log(`         - ${allergen.attribute_title}`);
          });
        } else {
          console.log(`      ⚠️  Allergens: None`);
        }

        if (ingredient.dietary_attributes && ingredient.dietary_attributes.length > 0) {
          console.log(`      🌱 Dietary (${ingredient.dietary_attributes.length}):`);
          ingredient.dietary_attributes.forEach(diet => {
            console.log(`         - ${diet.attribute_title}`);
          });
        } else {
          console.log(`      🌱 Dietary: None`);
        }

        if (ingredient.cuisine_attributes && ingredient.cuisine_attributes.length > 0) {
          console.log(`      🍽️  Cuisine (${ingredient.cuisine_attributes.length}):`);
          ingredient.cuisine_attributes.forEach(cuisine => {
            console.log(`         - ${cuisine.attribute_title}`);
          });
        } else {
          console.log(`      🍽️  Cuisine: None`);
        }

        console.log(''); // Empty line between ingredients
      });
    } else {
      console.log('🥕 No ingredients found in this recipe\n');
    }

    // Summary
    console.log('📊 Enhancement Summary:');
    console.log(`   ✅ Basic ingredient data: Available`);
    console.log(`   ✅ Enhanced structure: Available`);
    console.log(`   ✅ Categories support: Available`);
    console.log(`   ✅ Nutrition attributes: Available`);
    console.log(`   ✅ Allergen attributes: Available`);
    console.log(`   ✅ Dietary attributes: Available`);
    console.log(`   ✅ Cuisine attributes: Available`);

    console.log('\n🎉 Enhanced ingredients functionality test completed successfully!');

  } catch (error) {
    console.error('❌ Error testing enhanced ingredients:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEnhancedIngredients()
    .then(() => {
      console.log('\n✨ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testEnhancedIngredients };
