{"testCases": {"placeholderTests": {"keepExisting": {"name": "Keep Existing Placeholder", "input": {"recipe_title": "Updated Recipe"}, "existingData": {"recipe_placeholder": 123}, "expected": {"recipe_placeholder": 123, "behavior": "No change to placeholder"}}, "updateWithNew": {"name": "Update Placeholder with New Image", "input": {"recipe_title": "Updated Recipe", "files": {"recipePlaceholder": ["newImageFile.jpg"]}}, "existingData": {"recipe_placeholder": 123}, "expected": {"recipe_placeholder": "NEW_ITEM_ID", "behavior": "New image uploaded, old placeholder preserved in system"}}, "explicitRemove": {"name": "Remove Placeholder Explicitly", "input": {"recipe_title": "Updated Recipe", "recipe_placeholder": null}, "existingData": {"recipe_placeholder": 123}, "expected": {"recipe_placeholder": null, "behavior": "Placeholder removed, file remains in system"}}}, "resourceTests": {"keepAllExisting": {"name": "Keep All Existing Resources", "input": {"recipe_title": "Updated Recipe"}, "existingData": {"resources": [{"id": 1, "item_id": 101, "status": "active"}, {"id": 2, "item_id": 102, "status": "active"}]}, "expected": {"activeResources": [{"item_id": 101, "status": "active"}, {"item_id": 102, "status": "active"}], "inactiveResources": [], "totalActive": 2}}, "addNewOnly": {"name": "Add New Resources Only", "input": {"recipe_title": "Updated Recipe", "files": {"recipeFiles": ["newFile1.pdf", "newFile2.pdf"]}, "resources": [{"item_id": 101, "type": "item"}, {"item_id": 102, "type": "item"}]}, "existingData": {"resources": [{"id": 1, "item_id": 101, "status": "active"}, {"id": 2, "item_id": 102, "status": "active"}]}, "expected": {"activeResources": [{"item_id": 101, "status": "active"}, {"item_id": 102, "status": "active"}, {"item_id": "NEW_ITEM_ID_1", "status": "active"}, {"item_id": "NEW_ITEM_ID_2", "status": "active"}], "totalActive": 4, "newUploads": 2}}, "replaceSome": {"name": "Replace Some Resources", "input": {"recipe_title": "Updated Recipe", "files": {"recipeFiles": ["newFile1.pdf"]}, "resources": [{"item_id": 101, "type": "item"}]}, "existingData": {"resources": [{"id": 1, "item_id": 101, "status": "active"}, {"id": 2, "item_id": 102, "status": "active"}]}, "expected": {"activeResources": [{"item_id": 101, "status": "active"}, {"item_id": "NEW_ITEM_ID", "status": "active"}], "inactiveResources": [{"item_id": 102, "status": "inactive"}], "totalActive": 2, "totalInactive": 1}}, "removeAll": {"name": "Remove All Resources", "input": {"recipe_title": "Updated Recipe", "resources": []}, "existingData": {"resources": [{"id": 1, "item_id": 101, "status": "active"}, {"id": 2, "item_id": 102, "status": "active"}]}, "expected": {"activeResources": [], "inactiveResources": [{"item_id": 101, "status": "inactive"}, {"item_id": 102, "status": "inactive"}], "totalActive": 0, "totalInactive": 2}}}, "stepTests": {"keepAllExisting": {"name": "Keep All Existing Step Images", "input": {"recipe_title": "Updated Recipe", "steps": [{"order": 1, "description": "Step 1", "item_id": 301}, {"order": 2, "description": "Step 2", "item_id": 302}]}, "existingData": {"steps": [{"order": 1, "description": "Old Step 1", "item_id": 301}, {"order": 2, "description": "Old Step 2", "item_id": 302}]}, "expected": {"steps": [{"order": 1, "description": "Step 1", "item_id": 301}, {"order": 2, "description": "Step 2", "item_id": 302}], "behavior": "Steps recreated with preserved image references"}}, "addNewImages": {"name": "Add New Step Images", "input": {"recipe_title": "Updated Recipe", "files": {"stepImages": ["stepImage1.jpg", "stepImage2.jpg"]}, "steps": [{"order": 1, "description": "Step 1", "item_id": 301}, {"order": 2, "description": "Step 2"}, {"order": 3, "description": "Step 3"}]}, "existingData": {"steps": [{"order": 1, "description": "Old Step 1", "item_id": 301}]}, "expected": {"steps": [{"order": 1, "description": "Step 1", "item_id": 301}, {"order": 2, "description": "Step 2", "item_id": "NEW_ITEM_ID_1"}, {"order": 3, "description": "Step 3", "item_id": "NEW_ITEM_ID_2"}], "behavior": "Mixed existing and new step images"}}, "noStepsProvided": {"name": "No Steps Provided - Keep Existing", "input": {"recipe_title": "Updated Recipe"}, "existingData": {"steps": [{"order": 1, "description": "Step 1", "item_id": 301}, {"order": 2, "description": "Step 2", "item_id": 302}]}, "expected": {"steps": [{"order": 1, "description": "Step 1", "item_id": 301}, {"order": 2, "description": "Step 2", "item_id": 302}], "behavior": "Existing steps preserved when none provided"}}}, "mixedTests": {"completeUpdate": {"name": "Update All Media Types", "input": {"recipe_title": "Complete Update", "files": {"recipePlaceholder": ["newPlaceholder.jpg"], "recipeFiles": ["newResource1.pdf", "newResource2.pdf"], "stepImages": ["newStepImage1.jpg"]}, "resources": [{"item_id": 101, "type": "item"}], "steps": [{"order": 1, "description": "Step 1", "item_id": 301}, {"order": 2, "description": "Step 2"}]}, "existingData": {"recipe_placeholder": 123, "resources": [{"id": 1, "item_id": 101, "status": "active"}, {"id": 2, "item_id": 102, "status": "active"}], "steps": [{"order": 1, "description": "Old Step 1", "item_id": 301}]}, "expected": {"recipe_placeholder": "NEW_PLACEHOLDER_ID", "activeResources": 3, "inactiveResources": 1, "steps": 2, "behavior": "All media types updated with smart merge"}}, "partialUpdate": {"name": "Partial Media Update", "input": {"recipe_title": "Partial Update"}, "existingData": {"recipe_placeholder": 123, "resources": [{"id": 1, "item_id": 101, "status": "active"}, {"id": 2, "item_id": 102, "status": "active"}], "steps": [{"order": 1, "description": "Step 1", "item_id": 301}, {"order": 2, "description": "Step 2", "item_id": 302}]}, "expected": {"recipe_placeholder": 123, "activeResources": 2, "steps": 2, "behavior": "All existing media preserved"}}}, "edgeCases": {"invalidItemIds": {"name": "Invalid Item IDs in Resources", "input": {"resources": [{"item_id": 999999, "type": "item"}, {"item_id": 101, "type": "item"}]}, "existingData": {"resources": [{"id": 1, "item_id": 101, "status": "active"}]}, "expected": {"activeResources": 1, "behavior": "Invalid item_ids skipped, valid ones processed", "warnings": ["Item ID 999999 not found"]}}, "duplicateResources": {"name": "Duplicate Resources", "input": {"resources": [{"item_id": 101, "type": "item"}, {"item_id": 101, "type": "item"}]}, "expected": {"activeResources": 1, "behavior": "Duplicates handled gracefully"}}, "stepImageMismatch": {"name": "Step Image Count <PERSON>", "input": {"files": {"stepImages": ["image1.jpg", "image2.jpg", "image3.jpg"]}, "steps": [{"order": 1, "description": "Step 1"}, {"order": 2, "description": "Step 2"}]}, "expected": {"steps": [{"order": 1, "description": "Step 1", "item_id": "NEW_ITEM_ID_1"}, {"order": 2, "description": "Step 2", "item_id": "NEW_ITEM_ID_2"}], "behavior": "Extra images ignored, steps matched by order"}}}}, "testConfiguration": {"database": {"useTransaction": true, "rollbackAfterTest": true}, "fileUploads": {"simulateUploads": true, "mockS3Operations": true}, "validation": {"checkItemExists": true, "validateFileTypes": true}}}