# Recipe Update API Payloads - Smart Merge Implementation

## Endpoint
```
PUT /api/v1/recipes/update/:id
Content-Type: multipart/form-data
```

## Smart Merge Behavior Summary
- **Resources**: Only provided resources are kept active, others become inactive
- **Steps**: If steps provided, all existing steps are replaced; if not provided, existing steps are preserved
- **Placeholder**: If not provided, existing placeholder is kept; if provided, it's updated; if null, it's removed

---

## 1. Your Scenario: 3 New Files + 2 Existing Resources

### Frontend Payload
```javascript
const formData = new FormData();

// Basic recipe info
formData.append('recipe_title', 'Updated Recipe Title');
formData.append('recipe_description', 'Updated description');

// NEW FILES: 3 new files to upload
formData.append('recipeFiles', newFile1); // File object
formData.append('recipeFiles', newFile2); // File object  
formData.append('recipeFiles', newFile3); // File object

// EXISTING RESOURCES: 2 existing files to keep
formData.append('resources', JSON.stringify([
  {
    "item_id": 101,
    "type": "item",
    "item_link": null,
    "item_link_type": "document"
  },
  {
    "item_id": 102, 
    "type": "item",
    "item_link": null,
    "item_link_type": "document"
  }
]));
```

### Expected Result
- **Total Active Resources**: 5 (2 existing + 3 new)
- **Behavior**: Existing resources with item_id 101 & 102 preserved, 3 new files uploaded and added
- **Other existing resources**: Deactivated (if any existed beyond the 2 specified)

---

## 2. Keep All Existing Media (No Changes)

### Frontend Payload
```javascript
const formData = new FormData();

// Only update basic info
formData.append('recipe_title', 'Updated Title Only');
formData.append('recipe_description', 'Updated description only');

// NO media fields provided
// - No recipeFiles
// - No recipePlaceholder  
// - No stepImages
// - No resources array
// - No steps array
```

### Expected Result
- **Placeholder**: Unchanged
- **Resources**: All existing resources remain active
- **Steps**: All existing steps remain unchanged
- **Behavior**: Pure basic info update, all media preserved

---

## 3. Complete Media Update

### Frontend Payload
```javascript
const formData = new FormData();

// Basic info
formData.append('recipe_title', 'Complete Update');

// NEW PLACEHOLDER
formData.append('recipePlaceholder', newPlaceholderFile);

// NEW RECIPE FILES + EXISTING
formData.append('recipeFiles', newResourceFile1);
formData.append('recipeFiles', newResourceFile2);
formData.append('resources', JSON.stringify([
  {
    "item_id": 101,
    "type": "item"
  }
]));

// NEW STEP IMAGES + EXISTING
formData.append('stepImages', newStepImage1);
formData.append('steps', JSON.stringify([
  {
    "order": 1,
    "description": "Updated Step 1", 
    "item_id": 301  // keep existing image
  },
  {
    "order": 2,
    "description": "New Step 2"
    // new image from stepImages upload
  },
  {
    "order": 3,
    "description": "Step without image"
    // no image
  }
]));
```

### Expected Result
- **Placeholder**: Updated with new image
- **Resources**: 1 existing + 2 new = 3 total active
- **Steps**: 3 steps (1 with existing image, 1 with new image, 1 without image)

---

## 4. Remove Specific Media

### Frontend Payload
```javascript
const formData = new FormData();

// Basic info
formData.append('recipe_title', 'Remove Media Example');

// REMOVE PLACEHOLDER
formData.append('recipe_placeholder', 'null'); // explicit null as string

// KEEP ONLY SOME RESOURCES (others will be deactivated)
formData.append('resources', JSON.stringify([
  {
    "item_id": 101,
    "type": "item"
  }
  // item_id 102 not included, so it will be deactivated
]));

// REMOVE ALL STEPS
formData.append('steps', JSON.stringify([])); // empty array
```

### Expected Result
- **Placeholder**: Removed (set to null)
- **Resources**: Only item_id 101 active, others deactivated
- **Steps**: All existing steps deleted

---

## 5. Mixed Step Updates

### Frontend Payload
```javascript
const formData = new FormData();

formData.append('recipe_title', 'Mixed Step Update');

// Upload new step images
formData.append('stepImages', newStepImage1);
formData.append('stepImages', newStepImage2);

// Define steps with mix of existing and new images
formData.append('steps', JSON.stringify([
  {
    "order": 1,
    "description": "Keep existing image",
    "item_id": 301  // existing step image
  },
  {
    "order": 2, 
    "description": "New image from upload"
    // Will get newStepImage1 (matched by order)
  },
  {
    "order": 3,
    "description": "Another new image" 
    // Will get newStepImage2 (matched by order)
  },
  {
    "order": 4,
    "description": "No image step"
    // No image assigned
  }
]));
```

### Expected Result
- **Steps**: 4 steps total
  - Step 1: Existing image (item_id: 301)
  - Step 2: New image from upload
  - Step 3: New image from upload  
  - Step 4: No image (item_id: null)

---

## 6. Add Only New Resources (Keep All Existing)

### Frontend Payload
```javascript
const formData = new FormData();

formData.append('recipe_title', 'Add New Resources');

// Upload new files
formData.append('recipeFiles', newFile1);
formData.append('recipeFiles', newFile2);

// Include ALL existing resources + let system add new ones
formData.append('resources', JSON.stringify([
  // ALL existing resources must be listed to keep them
  { "item_id": 101, "type": "item" },
  { "item_id": 102, "type": "item" },
  { "item_id": 103, "type": "item" },
  // New files will be automatically added by the system
]));
```

### Expected Result
- **Resources**: All existing (101, 102, 103) + 2 new files = 5 total active
- **Behavior**: System merges existing resources with new uploads

---

## 7. Link Resources (No File Upload)

### Frontend Payload
```javascript
const formData = new FormData();

formData.append('recipe_title', 'Link Resources');

// Add link resources (no file uploads)
formData.append('resources', JSON.stringify([
  {
    "item_id": 101,
    "type": "item"  // existing file resource
  },
  {
    "type": "link",
    "item_link": "https://youtube.com/watch?v=example",
    "item_link_type": "youtube"
  },
  {
    "type": "link", 
    "item_link": "https://example.com/recipe-guide.pdf",
    "item_link_type": "pdf"
  }
]));
```

### Expected Result
- **Resources**: 1 existing file + 2 new links = 3 total active
- **Behavior**: Mix of file and link resources

---

## 8. Error Handling Examples

### Invalid Item IDs
```javascript
const formData = new FormData();

formData.append('resources', JSON.stringify([
  { "item_id": 999999, "type": "item" }, // Non-existent item_id
  { "item_id": 101, "type": "item" }     // Valid item_id
]));
```

### Expected Result
- **Behavior**: Invalid item_id skipped, valid ones processed
- **Response**: Warning logged, only valid resources created

---

## Key Implementation Notes

### 1. Smart Merge Logic
- **Resources**: `smartMerge: true` preserves existing when not explicitly replaced
- **Steps**: `smartMerge: true` preserves existing when steps array not provided
- **Placeholder**: Smart logic handles undefined vs null vs new file

### 2. File Upload Matching
- **Step Images**: Matched to steps by order (stepImages[0] → step with order 1)
- **Recipe Files**: Added to resources array automatically
- **Placeholder**: Single file, replaces existing if provided

### 3. Backward Compatibility
- **Existing API calls**: Continue to work unchanged
- **Legacy behavior**: Available by setting `smartMerge: false` in helper functions
- **Gradual migration**: Can be implemented incrementally

### 4. Validation
- **Item IDs**: Validated against existing items in database
- **File types**: Validated by multer configuration
- **Required fields**: Only recipe_title required for updates (all others optional)

This Smart Merge implementation provides the **minimal 2-key addition** approach you requested while handling all the complex scenarios for managing existing vs new media files during recipe updates.
