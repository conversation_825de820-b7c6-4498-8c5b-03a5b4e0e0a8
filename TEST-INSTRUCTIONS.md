# Recipe Update API - Smart Merge Testing Instructions

## 🎯 Test Objective
Validate the Smart Merge implementation for handling **3 new files + 2 existing files** scenario in recipe updates.

---

## 🛠️ Test Options

### Option 1: Quick Curl Test (Recommended)
```bash
# 1. Update configuration in test-recipe-update-curl.sh
API_BASE_URL="http://localhost:3000/api/v1"  # Your API URL
AUTH_TOKEN="your-actual-auth-token"          # Your auth token
RECIPE_ID="1"                                # Recipe ID to test

# 2. Make script executable and run
chmod +x test-recipe-update-curl.sh
./test-recipe-update-curl.sh
```

### Option 2: Node.js Test Script
```bash
# 1. Install dependencies
npm install axios form-data

# 2. Update configuration in test-recipe-update-api.js
const API_BASE_URL = 'http://localhost:3000/api/v1';
const AUTH_TOKEN = 'your-actual-auth-token';
const RECIPE_ID = 1;

# 3. Run test
node test-recipe-update-api.js
```

### Option 3: Postman Collection
1. Import `Recipe-Update-Smart-Merge-Tests.postman_collection.json` into Postman
2. Update collection variables:
   - `baseUrl`: Your API base URL
   - `authToken`: Your authentication token
   - `recipeId`: Recipe ID to test
3. Run the collection

---

## 📋 Test Scenarios Covered

### 1. **Complete Smart Merge Test** (Main Test)
**Payload:**
- ✅ 3 new resource files (PDF, DOC, Image)
- ✅ 2 existing resources to keep (item_id: 101, 102)
- ✅ 3 link resources (YouTube, PDF, Link)
- ✅ New placeholder image
- ✅ 2 new step images
- ✅ 5 steps (2 with existing images, 2 with new images, 1 without)

**Expected Result:**
- Total active resources: 8 (2 existing + 3 new files + 3 links)
- Total steps: 5
- Placeholder: Updated
- Other existing resources: Deactivated

### 2. **Keep All Existing Media**
**Payload:**
- Only basic recipe info (title, description)
- No media fields provided

**Expected Result:**
- All existing media preserved
- No files uploaded or processed

### 3. **Remove All Resources**
**Payload:**
- Empty resources array: `[]`

**Expected Result:**
- All existing resources deactivated
- No active resources remain

---

## 🔍 What to Verify

### Before Update:
```bash
GET /api/v1/recipes/{id}
```
Note current state:
- Number of resources
- Number of steps
- Placeholder ID

### After Update:
```bash
GET /api/v1/recipes/{id}
```
Verify changes:
- ✅ Resources count increased correctly
- ✅ Steps updated with proper images
- ✅ Existing specified resources preserved
- ✅ New files uploaded and linked
- ✅ Link resources added
- ✅ Unspecified existing resources deactivated

---

## 📊 Expected Test Results

### Smart Merge Validation:
```
BEFORE UPDATE:
- Resources: X existing
- Steps: Y existing
- Placeholder: Z

AFTER UPDATE:
- Active Resources: 8 total
  - 2 existing files (preserved)
  - 3 new uploaded files
  - 3 link resources
- Steps: 5 total
  - Step 1: Existing image (item_id: 301)
  - Step 2: New image from upload
  - Step 3: Existing image (item_id: 303)
  - Step 4: New image from upload
  - Step 5: No image
- Placeholder: New uploaded image
```

---

## 🚨 Troubleshooting

### Common Issues:

1. **Authentication Error (401)**
   - Update `AUTH_TOKEN` with valid token
   - Check token expiration

2. **Recipe Not Found (404)**
   - Update `RECIPE_ID` with existing recipe
   - Verify recipe belongs to your organization

3. **File Upload Error**
   - Check file paths exist
   - Verify file permissions
   - Ensure files are not too large

4. **Invalid JSON in Resources/Steps**
   - Validate JSON syntax
   - Check item_ids exist in database

### Debug Steps:
1. Test basic GET request first
2. Check server logs for detailed errors
3. Verify database state before/after
4. Test with smaller payloads first

---

## 🎉 Success Criteria

✅ **Test passes if:**
- Update API returns 200 status
- Response indicates success (`status: true`)
- Recipe state shows expected changes:
  - 8 active resources (2 existing + 3 new + 3 links)
  - 5 steps with correct image assignments
  - New placeholder image
  - Unspecified existing resources deactivated

✅ **Smart Merge working if:**
- Existing specified resources preserved
- New files uploaded and added
- Link resources created
- Step images matched correctly
- No unexpected data loss

---

## 📝 Test Report Template

```
SMART MERGE TEST RESULTS
========================

Test Date: ___________
API Endpoint: PUT /api/v1/recipes/update/{id}
Recipe ID: ___________

BEFORE UPDATE:
- Resources: _____ total
- Steps: _____ total
- Placeholder: _____

AFTER UPDATE:
- Active Resources: _____ total
  - File Resources: _____
  - Link Resources: _____
- Steps: _____ total
- Steps with Images: _____
- Placeholder: Updated? _____

VALIDATION:
□ 3 new files uploaded
□ 2 existing files preserved
□ 3 link resources added
□ Step images assigned correctly
□ Unspecified resources deactivated
□ No data corruption

RESULT: PASS / FAIL
NOTES: ________________
```

Run the tests and verify that your Smart Merge implementation handles the **3 new + 2 existing** scenario perfectly!
